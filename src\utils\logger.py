#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام السجلات
Logging System
"""

import logging
import os
from datetime import datetime

def setup_logger(name: str = "camera_monitor", log_level: str = "INFO") -> logging.Logger:
    """
    إعداد نظام السجلات
    
    Args:
        name: اسم السجل
        log_level: مستوى السجل
        
    Returns:
        logging.Logger: كائن السجل
    """
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # إنشاء اسم ملف السجل مع التاريخ
    log_filename = f"{log_dir}/camera_monitor_{datetime.now().strftime('%Y%m%d')}.log"
    
    # إعداد التنسيق
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد السجل
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # تجنب إضافة handlers متعددة
    if not logger.handlers:
        # معالج الملف
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger
