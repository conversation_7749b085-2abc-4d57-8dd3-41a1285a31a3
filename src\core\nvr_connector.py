#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الاتصال بأجهزة NVR
NVR Connection Module

هذه الوحدة مسؤولة عن الاتصال مع أجهزة NVR المختلفة وجلب معلومات الكاميرات
"""

import requests
import json
import cv2
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

class NVRConnector:
    """كلاس للاتصال مع أجهزة NVR"""
    
    def __init__(self, nvr_ip: str, port: int = 8000, username: str = "admin", password: str = ""):
        """
        تهيئة الاتصال مع جهاز NVR
        
        Args:
            nvr_ip: عنوان IP الخاص بجهاز NVR
            port: منفذ الاتصال (افتراضي 8000)
            username: اسم المستخدم
            password: كلمة المرور
        """
        self.nvr_ip = nvr_ip
        self.port = port
        self.username = username
        self.password = password
        self.base_url = f"http://{nvr_ip}:{port}"
        self.session = requests.Session()
        self.is_connected = False
        self.cameras = {}
        self.logger = logging.getLogger(__name__)
        
        # محاولة الاتصال
        self.connect()
    
    def connect(self) -> bool:
        """
        الاتصال بجهاز NVR
        
        Returns:
            bool: True إذا نجح الاتصال، False إذا فشل
        """
        try:
            # محاولة تسجيل الدخول
            login_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = self.session.post(
                f"{self.base_url}/api/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.is_connected = True
                self.logger.info(f"تم الاتصال بنجاح مع NVR: {self.nvr_ip}")
                self._load_cameras()
                return True
            else:
                self.logger.error(f"فشل في تسجيل الدخول إلى NVR: {self.nvr_ip}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في الاتصال مع NVR {self.nvr_ip}: {str(e)}")
            self.is_connected = False
            return False
    
    def _load_cameras(self):
        """تحميل قائمة الكاميرات من جهاز NVR"""
        try:
            response = self.session.get(f"{self.base_url}/api/cameras")
            if response.status_code == 200:
                cameras_data = response.json()
                for camera in cameras_data.get('cameras', []):
                    camera_id = camera.get('id')
                    self.cameras[camera_id] = {
                        'id': camera_id,
                        'name': camera.get('name', f'Camera {camera_id}'),
                        'ip': camera.get('ip'),
                        'port': camera.get('port', 554),
                        'rtsp_url': camera.get('rtsp_url'),
                        'status': 'unknown',
                        'last_check': None,
                        'quality': 0,
                        'fps': 0
                    }
                self.logger.info(f"تم تحميل {len(self.cameras)} كاميرا من NVR: {self.nvr_ip}")
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الكاميرات من NVR {self.nvr_ip}: {str(e)}")
    
    def get_cameras(self) -> Dict:
        """
        الحصول على قائمة الكاميرات
        
        Returns:
            Dict: قاموس يحتوي على معلومات الكاميرات
        """
        return self.cameras
    
    def check_camera_status(self, camera_id: str) -> Dict:
        """
        فحص حالة كاميرا معينة
        
        Args:
            camera_id: معرف الكاميرا
            
        Returns:
            Dict: معلومات حالة الكاميرا
        """
        if camera_id not in self.cameras:
            return {'status': 'not_found', 'message': 'الكاميرا غير موجودة'}
        
        camera = self.cameras[camera_id]
        
        try:
            # فحص الاتصال عبر ping
            ping_result = self._ping_camera(camera['ip'])
            
            if ping_result:
                # فحص جودة الفيديو
                quality_result = self._check_video_quality(camera)
                camera['status'] = 'online'
                camera['quality'] = quality_result.get('quality', 0)
                camera['fps'] = quality_result.get('fps', 0)
            else:
                camera['status'] = 'offline'
                camera['quality'] = 0
                camera['fps'] = 0
            
            camera['last_check'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص الكاميرا {camera_id}: {str(e)}")
            camera['status'] = 'error'
            camera['quality'] = 0
            camera['fps'] = 0
        
        return camera
    
    def _ping_camera(self, camera_ip: str) -> bool:
        """
        فحص الاتصال مع الكاميرا عبر ping
        
        Args:
            camera_ip: عنوان IP الخاص بالكاميرا
            
        Returns:
            bool: True إذا كانت الكاميرا متاحة
        """
        try:
            import subprocess
            import platform
            
            # تحديد أمر ping حسب نظام التشغيل
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", "3000", camera_ip]
            else:
                cmd = ["ping", "-c", "1", "-W", "3", camera_ip]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            return result.returncode == 0
            
        except Exception:
            return False
    
    def _check_video_quality(self, camera: Dict) -> Dict:
        """
        فحص جودة الفيديو للكاميرا
        
        Args:
            camera: معلومات الكاميرا
            
        Returns:
            Dict: معلومات جودة الفيديو
        """
        try:
            rtsp_url = camera.get('rtsp_url')
            if not rtsp_url:
                return {'quality': 0, 'fps': 0}
            
            # فتح اتصال الفيديو
            cap = cv2.VideoCapture(rtsp_url)
            
            if not cap.isOpened():
                return {'quality': 0, 'fps': 0}
            
            # قراءة عدة إطارات لحساب FPS وجودة الصورة
            frame_count = 0
            start_time = time.time()
            total_quality = 0
            
            for _ in range(10):  # فحص 10 إطارات
                ret, frame = cap.read()
                if ret:
                    frame_count += 1
                    # حساب جودة الإطار (بناءً على الوضوح)
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    quality = cv2.Laplacian(gray, cv2.CV_64F).var()
                    total_quality += quality
                else:
                    break
            
            cap.release()
            
            if frame_count > 0:
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_quality = total_quality / frame_count
                
                # تحويل جودة الصورة إلى نسبة مئوية
                quality_percentage = min(100, max(0, (avg_quality / 1000) * 100))
                
                return {
                    'quality': round(quality_percentage, 2),
                    'fps': round(fps, 2)
                }
            
            return {'quality': 0, 'fps': 0}
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص جودة الفيديو: {str(e)}")
            return {'quality': 0, 'fps': 0}
    
    def get_camera_stream(self, camera_id: str) -> Optional[str]:
        """
        الحصول على رابط البث المباشر للكاميرا
        
        Args:
            camera_id: معرف الكاميرا
            
        Returns:
            Optional[str]: رابط البث المباشر أو None
        """
        if camera_id in self.cameras:
            return self.cameras[camera_id].get('rtsp_url')
        return None
    
    def disconnect(self):
        """قطع الاتصال مع جهاز NVR"""
        try:
            if self.is_connected:
                self.session.post(f"{self.base_url}/api/logout")
                self.is_connected = False
                self.logger.info(f"تم قطع الاتصال مع NVR: {self.nvr_ip}")
        except Exception as e:
            self.logger.error(f"خطأ في قطع الاتصال مع NVR {self.nvr_ip}: {str(e)}")
