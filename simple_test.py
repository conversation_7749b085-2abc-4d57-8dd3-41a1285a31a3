#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام مراقبة الكاميرات (نسخة نصية)
Simple Camera Monitoring System Test (Console Version)
"""

import random
import time
from datetime import datetime
import os

class SimpleCameraMonitor:
    """مراقب كاميرات مبسط للاختبار"""
    
    def __init__(self):
        self.cameras = {}
        self.monitoring = False
        self._create_demo_cameras()
    
    def _create_demo_cameras(self):
        """إنشاء كاميرات تجريبية"""
        camera_names = [
            "كاميرا المدخل الرئيسي",
            "كاميرا الاستقبال", 
            "كاميرا الممر الأول",
            "كاميرا المكتب",
            "كاميرا الخروج الطارئ",
            "كاميرا موقف السيارات"
        ]
        
        for i, name in enumerate(camera_names, 1):
            self.cameras[f"CAM_{i:02d}"] = {
                'name': name,
                'status': 'online',
                'quality': random.randint(70, 100),
                'fps': random.randint(20, 30),
                'last_check': datetime.now(),
                'ip': f"192.168.1.{100 + i}"
            }
    
    def check_cameras(self):
        """فحص حالة الكاميرات"""
        alerts = []
        
        for cam_id, camera in self.cameras.items():
            # محاكاة مشاكل عشوائية
            problem_chance = random.randint(1, 100)
            
            if problem_chance <= 15:  # 15% احتمال مشكلة
                if problem_chance <= 5:  # 5% انقطاع
                    camera['status'] = 'offline'
                    camera['quality'] = 0
                    camera['fps'] = 0
                    alerts.append(f"⚠️  انقطع الاتصال مع {camera['name']} ({camera['ip']})")
                elif problem_chance <= 10:  # 5% جودة منخفضة
                    camera['status'] = 'poor_quality'
                    camera['quality'] = random.randint(20, 50)
                    camera['fps'] = random.randint(5, 15)
                    alerts.append(f"⚠️  جودة منخفضة في {camera['name']} ({camera['quality']}%)")
                else:  # 5% FPS منخفض
                    camera['status'] = 'low_fps'
                    camera['quality'] = random.randint(60, 80)
                    camera['fps'] = random.randint(5, 12)
                    alerts.append(f"⚠️  معدل إطارات منخفض في {camera['name']} ({camera['fps']} FPS)")
            else:  # 85% عمل طبيعي
                camera['status'] = 'online'
                camera['quality'] = random.randint(70, 100)
                camera['fps'] = random.randint(20, 30)
            
            camera['last_check'] = datetime.now()
        
        return alerts
    
    def get_status_summary(self):
        """ملخص حالة النظام"""
        total = len(self.cameras)
        online = sum(1 for c in self.cameras.values() if c['status'] == 'online')
        offline = sum(1 for c in self.cameras.values() if c['status'] == 'offline')
        problems = total - online - offline
        
        return {
            'total': total,
            'online': online,
            'offline': offline,
            'problems': problems
        }

def clear_screen():
    """مسح الشاشة"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """طباعة رأس الصفحة"""
    print("=" * 70)
    print("🎥 نظام مراقبة الكاميرات - برنامج فيكتور")
    print("   Camera Monitoring System - Vector Program")
    print("=" * 70)

def print_camera_grid(cameras):
    """طباعة شبكة الكاميرات"""
    print("\n📊 حالة الكاميرات:")
    print("-" * 70)
    
    for cam_id, camera in cameras.items():
        # تحديد الرمز والحالة
        if camera['status'] == 'online':
            status_icon = "🟢"
            status_text = "متصلة"
        elif camera['status'] == 'offline':
            status_icon = "🔴"
            status_text = "منقطعة"
        elif camera['status'] == 'poor_quality':
            status_icon = "🟡"
            status_text = "جودة منخفضة"
        elif camera['status'] == 'low_fps':
            status_icon = "🟠"
            status_text = "FPS منخفض"
        else:
            status_icon = "⚪"
            status_text = "غير معروف"
        
        print(f"{status_icon} {cam_id}: {camera['name']:<25} | {status_text:<12} | "
              f"جودة: {camera['quality']:3d}% | FPS: {camera['fps']:2d} | IP: {camera['ip']}")

def print_alerts(alerts):
    """طباعة التنبيهات"""
    if alerts:
        print(f"\n🚨 التنبيهات ({len(alerts)}):")
        print("-" * 70)
        for i, alert in enumerate(alerts, 1):
            print(f"{i:2d}. {alert}")
    else:
        print(f"\n✅ لا توجد تنبيهات - جميع الكاميرات تعمل بشكل طبيعي")

def print_summary(status):
    """طباعة ملخص الحالة"""
    print(f"\n📈 ملخص النظام:")
    print("-" * 70)
    print(f"📊 إجمالي الكاميرات: {status['total']}")
    print(f"🟢 متصلة وتعمل بشكل طبيعي: {status['online']}")
    print(f"🔴 منقطعة: {status['offline']}")
    print(f"⚠️  بها مشاكل: {status['problems']}")
    
    # حساب النسب المئوية
    if status['total'] > 0:
        online_percent = (status['online'] / status['total']) * 100
        print(f"📊 نسبة الكاميرات العاملة: {online_percent:.1f}%")

def main():
    """الدالة الرئيسية"""
    monitor = SimpleCameraMonitor()
    
    print_header()
    print(f"\n🚀 تم إنشاء {len(monitor.cameras)} كاميرات تجريبية")
    print("💡 سيتم محاكاة مشاكل عشوائية للاختبار")
    print("⏱️  سيتم تحديث الحالة كل 5 ثوان")
    print("\n🔄 بدء المراقبة... (اضغط Ctrl+C للإيقاف)")
    
    input("\n📝 اضغط Enter للمتابعة...")
    
    try:
        cycle = 0
        while True:
            cycle += 1
            
            # مسح الشاشة وطباعة الرأس
            clear_screen()
            print_header()
            print(f"🔄 دورة المراقبة #{cycle} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # فحص الكاميرات
            alerts = monitor.check_cameras()
            status = monitor.get_status_summary()
            
            # عرض النتائج
            print_camera_grid(monitor.cameras)
            print_alerts(alerts)
            print_summary(status)
            
            print(f"\n⏱️  التحديث التالي خلال 5 ثوان... (Ctrl+C للإيقاف)")
            
            # انتظار 5 ثوان
            time.sleep(5)
            
    except KeyboardInterrupt:
        clear_screen()
        print_header()
        print("\n⏹️  تم إيقاف المراقبة بواسطة المستخدم")
        print("📊 إحصائيات الجلسة:")
        print(f"   • عدد دورات المراقبة: {cycle}")
        print(f"   • عدد الكاميرات المراقبة: {len(monitor.cameras)}")
        print("\n🙏 شكراً لاستخدام نظام مراقبة الكاميرات!")
        print("=" * 70)

if __name__ == "__main__":
    main()
