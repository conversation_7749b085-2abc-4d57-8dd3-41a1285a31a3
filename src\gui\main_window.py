#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للنظام
Main Window
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QPushButton, QTextEdit, 
                             QGroupBox, QScrollArea, QFrame, QMessageBox,
                             QDialog, QLineEdit, QSpinBox, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
import logging

class MainWindow(QMainWindow):
    """النافذة الرئيسية لنظام مراقبة الكاميرات"""
    
    def __init__(self):
        super().__init__()
        self.camera_monitor = None
        self.camera_widgets = {}
        self.logger = logging.getLogger(__name__)
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام مراقبة الكاميرات - فيكتور")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        
        # الجانب الأيسر - شبكة الكاميرات
        self.setup_camera_grid(main_layout)
        
        # الجانب الأيمن - لوحة التحكم
        self.setup_control_panel(main_layout)
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز للعمل")
        
    def setup_camera_grid(self, main_layout):
        """إعداد شبكة عرض الكاميرات"""
        # مجموعة الكاميرات
        camera_group = QGroupBox("الكاميرات")
        camera_layout = QVBoxLayout(camera_group)
        
        # منطقة التمرير للكاميرات
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.camera_grid_layout = QGridLayout(scroll_widget)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        camera_layout.addWidget(scroll_area)
        
        main_layout.addWidget(camera_group, 3)  # 75% من العرض
        
    def setup_control_panel(self, main_layout):
        """إعداد لوحة التحكم"""
        control_group = QGroupBox("لوحة التحكم")
        control_layout = QVBoxLayout(control_group)
        
        # أزرار التحكم
        self.add_nvr_btn = QPushButton("إضافة NVR")
        self.add_nvr_btn.clicked.connect(self.add_nvr_dialog)
        control_layout.addWidget(self.add_nvr_btn)
        
        self.start_monitor_btn = QPushButton("بدء المراقبة")
        self.start_monitor_btn.clicked.connect(self.start_monitoring)
        control_layout.addWidget(self.start_monitor_btn)
        
        self.stop_monitor_btn = QPushButton("إيقاف المراقبة")
        self.stop_monitor_btn.clicked.connect(self.stop_monitoring)
        self.stop_monitor_btn.setEnabled(False)
        control_layout.addWidget(self.stop_monitor_btn)
        
        # منطقة التنبيهات
        alerts_group = QGroupBox("التنبيهات")
        alerts_layout = QVBoxLayout(alerts_group)
        
        self.alerts_text = QTextEdit()
        self.alerts_text.setMaximumHeight(200)
        self.alerts_text.setReadOnly(True)
        alerts_layout.addWidget(self.alerts_text)
        
        control_layout.addWidget(alerts_group)
        
        # معلومات النظام
        info_group = QGroupBox("معلومات النظام")
        info_layout = QVBoxLayout(info_group)
        
        self.info_label = QLabel("عدد الكاميرات: 0\nالكاميرات المتصلة: 0\nالكاميرات المنقطعة: 0")
        info_layout.addWidget(self.info_label)
        
        control_layout.addWidget(info_group)
        
        control_layout.addStretch()
        main_layout.addWidget(control_group, 1)  # 25% من العرض
        
    def setup_timer(self):
        """إعداد مؤقت تحديث الواجهة"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(5000)  # تحديث كل 5 ثوان
        
    def set_camera_monitor(self, camera_monitor):
        """ربط مراقب الكاميرات بالواجهة"""
        self.camera_monitor = camera_monitor
        
        # ربط الإشارات
        camera_monitor.camera_status_changed.connect(self.on_camera_status_changed)
        camera_monitor.alert_triggered.connect(self.on_alert_triggered)
        
    @pyqtSlot(str, dict)
    def on_camera_status_changed(self, camera_id, status):
        """معالج تغيير حالة الكاميرا"""
        self.update_camera_widget(camera_id, status)
        
    @pyqtSlot(str, str, str)
    def on_alert_triggered(self, alert_type, camera_id, message):
        """معالج التنبيهات"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        alert_text = f"[{timestamp}] {message}\n"
        self.alerts_text.append(alert_text)
        
        # تمرير النص لأسفل
        scrollbar = self.alerts_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_camera_widget(self, camera_id, status):
        """تحديث ويدجت الكاميرا"""
        if camera_id not in self.camera_widgets:
            self.create_camera_widget(camera_id, status)
        else:
            widget = self.camera_widgets[camera_id]
            self.update_widget_status(widget, status)
            
    def create_camera_widget(self, camera_id, status):
        """إنشاء ويدجت جديد للكاميرا"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setFixedSize(200, 150)
        
        layout = QVBoxLayout(widget)
        
        # اسم الكاميرا
        name_label = QLabel(status.get('name', camera_id))
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(name_label)
        
        # حالة الكاميرا
        status_label = QLabel()
        layout.addWidget(status_label)
        
        # معلومات إضافية
        info_label = QLabel()
        info_label.setFont(QFont("Arial", 8))
        layout.addWidget(info_label)
        
        # حفظ المراجع
        widget.name_label = name_label
        widget.status_label = status_label
        widget.info_label = info_label
        
        self.camera_widgets[camera_id] = widget
        
        # إضافة إلى الشبكة
        row = len(self.camera_widgets) // 4
        col = len(self.camera_widgets) % 4
        self.camera_grid_layout.addWidget(widget, row, col)
        
        # تحديث الحالة
        self.update_widget_status(widget, status)
        
    def update_widget_status(self, widget, status):
        """تحديث حالة ويدجت الكاميرا"""
        status_text = status['status']
        
        if status_text == 'online':
            widget.setStyleSheet("QFrame { background-color: #90EE90; }")
            status_display = "متصلة"
        elif status_text == 'offline':
            widget.setStyleSheet("QFrame { background-color: #FFB6C1; }")
            status_display = "منقطعة"
        else:
            widget.setStyleSheet("QFrame { background-color: #FFFFE0; }")
            status_display = "غير معروف"
            
        widget.status_label.setText(f"الحالة: {status_display}")
        
        # معلومات إضافية
        quality = status.get('quality', 0)
        fps = status.get('fps', 0)
        widget.info_label.setText(f"الجودة: {quality}%\nFPS: {fps}")
        
    def add_nvr_dialog(self):
        """حوار إضافة NVR جديد"""
        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة NVR جديد")
        dialog.setModal(True)
        
        layout = QFormLayout(dialog)
        
        # الحقول
        name_edit = QLineEdit()
        ip_edit = QLineEdit()
        port_edit = QSpinBox()
        port_edit.setRange(1, 65535)
        port_edit.setValue(8000)
        username_edit = QLineEdit()
        username_edit.setText("admin")
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)
        
        layout.addRow("اسم NVR:", name_edit)
        layout.addRow("عنوان IP:", ip_edit)
        layout.addRow("المنفذ:", port_edit)
        layout.addRow("اسم المستخدم:", username_edit)
        layout.addRow("كلمة المرور:", password_edit)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        ok_btn = QPushButton("موافق")
        cancel_btn = QPushButton("إلغاء")
        
        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)
        
        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addRow(buttons_layout)
        
        if dialog.exec_() == QDialog.Accepted:
            # إضافة NVR
            if self.camera_monitor:
                success = self.camera_monitor.add_nvr(
                    name_edit.text(),
                    ip_edit.text(),
                    port_edit.value(),
                    username_edit.text(),
                    password_edit.text()
                )
                
                if success:
                    QMessageBox.information(self, "نجح", "تم إضافة NVR بنجاح")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة NVR")
                    
    def start_monitoring(self):
        """بدء المراقبة"""
        if self.camera_monitor:
            self.camera_monitor.start_monitoring()
            self.start_monitor_btn.setEnabled(False)
            self.stop_monitor_btn.setEnabled(True)
            self.statusBar().showMessage("المراقبة نشطة")
            
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        if self.camera_monitor:
            self.camera_monitor.stop_monitoring()
            self.start_monitor_btn.setEnabled(True)
            self.stop_monitor_btn.setEnabled(False)
            self.statusBar().showMessage("المراقبة متوقفة")
            
    def update_display(self):
        """تحديث العرض"""
        if self.camera_monitor:
            cameras = self.camera_monitor.get_all_cameras()
            total = len(cameras)
            online = sum(1 for c in cameras.values() if c.get('status') == 'online')
            offline = total - online
            
            self.info_label.setText(
                f"عدد الكاميرات: {total}\n"
                f"الكاميرات المتصلة: {online}\n"
                f"الكاميرات المنقطعة: {offline}"
            )
