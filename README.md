# نظام مراقبة الكاميرات - فيكتور
## Camera Monitoring System - Vector

نظام شامل لمراقبة الكاميرات المتصلة بأجهزة NVR وكشف الأعطال والمخالفات في الوقت الفعلي.

## المميزات الرئيسية

- 🎥 **مراقبة متعددة الكاميرات**: دعم أجهزة NVR متعددة
- 🔍 **كشف الأعطال التلقائي**: رصد انقطاع الاتصال وجودة الصورة
- 🚨 **نظام التنبيهات**: إشعارات فورية عند حدوث مشاكل
- 📊 **واجهة مستخدم سهلة**: عرض مرئي لحالة جميع الكاميرات
- 📝 **نظام السجلات**: تسجيل مفصل لجميع الأحداث
- ⚙️ **إعدادات قابلة للتخصيص**: تحكم كامل في معايير المراقبة

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, Linux, macOS
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل العرض التوضيحي

```bash
python test_demo.py
```

### 3. تشغيل النظام الكامل

```bash
python main.py
```

## كيفية الاستخدام

### 1. إضافة أجهزة NVR

1. اضغط على زر "إضافة NVR"
2. أدخل معلومات الاتصال:
   - اسم NVR
   - عنوان IP
   - المنفذ (افتراضي: 8000)
   - اسم المستخدم
   - كلمة المرور

### 2. بدء المراقبة

1. اضغط على "بدء المراقبة"
2. سيبدأ النظام في فحص جميع الكاميرات كل 10 ثوان
3. ستظهر الكاميرات في الشبكة مع ألوان تدل على حالتها:
   - 🟢 **أخضر**: الكاميرا تعمل بشكل طبيعي
   - 🔴 **أحمر**: الكاميرا منقطعة
   - 🟡 **أصفر**: مشكلة في الجودة أو الأداء

### 3. مراقبة التنبيهات

- ستظهر التنبيهات في لوحة "التنبيهات" على اليمين
- تتضمن التنبيهات:
  - انقطاع الاتصال مع الكاميرا
  - انخفاض جودة الصورة
  - انخفاض معدل الإطارات (FPS)

## هيكل المشروع

```
نظام مراقبة الكاميرات/
├── main.py                 # الملف الرئيسي
├── test_demo.py            # العرض التوضيحي
├── requirements.txt        # المتطلبات
├── config/
│   └── settings.ini        # ملف الإعدادات
├── src/
│   ├── core/              # الوحدات الأساسية
│   │   ├── camera_monitor.py
│   │   └── nvr_connector.py
│   ├── gui/               # واجهة المستخدم
│   │   └── main_window.py
│   └── utils/             # الأدوات المساعدة
│       └── logger.py
└── logs/                  # ملفات السجلات
```

## الإعدادات

يمكن تخصيص النظام من خلال ملف `config/settings.ini`:

```ini
[MONITORING]
check_interval = 10        # فترة الفحص بالثواني
quality_threshold = 70     # حد جودة الصورة
max_offline_time = 60      # أقصى وقت انقطاع مسموح

[ALERTS]
enable_sound = true        # تفعيل الصوت
enable_popup = true        # تفعيل النوافذ المنبثقة
```

## استكشاف الأخطاء

### مشكلة: لا يمكن الاتصال بـ NVR

**الحلول:**
1. تأكد من صحة عنوان IP والمنفذ
2. تحقق من اتصال الشبكة
3. تأكد من صحة اسم المستخدم وكلمة المرور

### مشكلة: الكاميرات لا تظهر

**الحلول:**
1. تأكد من أن NVR متصل ويعمل
2. تحقق من إعدادات الكاميرات في NVR
3. راجع ملف السجل في مجلد `logs/`

### مشكلة: جودة الصورة منخفضة

**الحلول:**
1. تحقق من إعدادات الكاميرا
2. تأكد من سرعة الشبكة
3. قم بضبط `quality_threshold` في الإعدادات

## التطوير والتخصيص

### إضافة نوع NVR جديد

1. قم بتعديل `src/core/nvr_connector.py`
2. أضف دعم البروتوكول الجديد
3. اختبر الاتصال

### إضافة نوع تنبيه جديد

1. قم بتعديل `src/core/camera_monitor.py`
2. أضف منطق كشف المشكلة الجديدة
3. أضف معالج التنبيه في الواجهة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع ملفات السجل في مجلد `logs/`
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من إعدادات الشبكة والاتصال

## الترخيص

هذا المشروع مطور لبرنامج فيكتور ومخصص للاستخدام الداخلي.

---

**ملاحظة**: العرض التوضيحي (`test_demo.py`) يستخدم بيانات محاكاة لأغراض الاختبار والتجربة.
