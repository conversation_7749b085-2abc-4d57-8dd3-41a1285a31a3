@echo off
echo ========================================
echo    نظام مراقبة الكاميرات - فيكتور
echo    Camera Monitoring System - Vector
echo ========================================
echo.

echo تحقق من تثبيت Python...
python --version
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo.
echo تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo تشغيل العرض التوضيحي...
echo اضغط Ctrl+C لإيقاف البرنامج
echo.

python test_demo.py

echo.
echo تم إنهاء البرنامج
pause
