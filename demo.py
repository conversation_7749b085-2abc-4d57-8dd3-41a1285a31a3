#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("=" * 50)
print("🎥 نظام مراقبة الكاميرات - فيكتور")
print("   Camera Monitoring System - Vector")
print("=" * 50)

import random
import time
from datetime import datetime

# إنشاء كاميرات تجريبية
cameras = {
    "CAM_01": {"name": "كاميرا المدخل الرئيسي", "ip": "*************"},
    "CAM_02": {"name": "كاميرا الاستقبال", "ip": "*************"},
    "CAM_03": {"name": "كاميرا الممر الأول", "ip": "*************"},
    "CAM_04": {"name": "كاميرا المكتب", "ip": "*************"},
    "CAM_05": {"name": "كاميرا الخروج الطارئ", "ip": "*************"},
    "CAM_06": {"name": "كاميرا موقف السيارات", "ip": "*************"}
}

print(f"\n🚀 تم إنشاء {len(cameras)} كاميرات تجريبية:")
for cam_id, info in cameras.items():
    print(f"  • {cam_id}: {info['name']} ({info['ip']})")

print("\n💡 سيتم محاكاة مشاكل عشوائية للاختبار")
print("🔄 بدء المراقبة...")

try:
    for cycle in range(1, 6):  # 5 دورات فقط للاختبار
        print(f"\n--- دورة المراقبة #{cycle} ---")
        print(f"⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}")
        
        alerts = []
        online_count = 0
        
        for cam_id, info in cameras.items():
            # محاكاة حالة عشوائية
            status_chance = random.randint(1, 100)
            
            if status_chance <= 10:  # 10% انقطاع
                status = "🔴 منقطعة"
                quality = 0
                alerts.append(f"⚠️ انقطع الاتصال مع {info['name']}")
            elif status_chance <= 25:  # 15% مشكلة
                status = "🟡 مشكلة"
                quality = random.randint(30, 60)
                alerts.append(f"⚠️ جودة منخفضة في {info['name']} ({quality}%)")
            else:  # 75% طبيعي
                status = "🟢 متصلة"
                quality = random.randint(70, 100)
                online_count += 1
            
            fps = random.randint(15, 30) if quality > 0 else 0
            print(f"  {cam_id}: {status:<12} | جودة: {quality:3d}% | FPS: {fps:2d}")
        
        print(f"\n📊 الملخص: {online_count}/{len(cameras)} كاميرات متصلة")
        
        if alerts:
            print("🚨 التنبيهات:")
            for alert in alerts:
                print(f"  {alert}")
        else:
            print("✅ لا توجد مشاكل")
        
        if cycle < 5:
            print("⏱️ انتظار 3 ثوان...")
            time.sleep(3)

except KeyboardInterrupt:
    print("\n⏹️ تم إيقاف المراقبة")

print("\n🎉 انتهى العرض التوضيحي!")
print("شكراً لاستخدام نظام مراقبة الكاميرات")
print("=" * 50)
