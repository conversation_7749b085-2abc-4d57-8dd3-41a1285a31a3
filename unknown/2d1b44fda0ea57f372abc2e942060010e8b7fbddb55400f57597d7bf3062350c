#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب الكاميرات الرئيسي
Main Camera Monitor

هذه الوحدة مسؤولة عن مراقبة جميع الكاميرات وكشف الأعطال
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Callable
import logging
from PyQt5.QtCore import QObject, pyqtSignal

from .nvr_connector import NVRConnector

class CameraMonitor(QObject):
    """مراقب الكاميرات الرئيسي"""
    
    # إشارات PyQt للتواصل مع الواجهة
    camera_status_changed = pyqtSignal(str, dict)  # camera_id, status_info
    alert_triggered = pyqtSignal(str, str, str)    # alert_type, camera_id, message
    
    def __init__(self):
        super().__init__()
        self.nvr_connectors = {}
        self.monitoring_active = False
        self.monitor_thread = None
        self.check_interval = 10  # ثواني
        self.logger = logging.getLogger(__name__)
        
        # إعدادات كشف المشاكل
        self.quality_threshold = 70
        self.max_offline_time = 60  # ثواني
        
    def add_nvr(self, nvr_id: str, nvr_ip: str, port: int = 8000, 
                username: str = "admin", password: str = ""):
        """
        إضافة جهاز NVR جديد للمراقبة
        
        Args:
            nvr_id: معرف فريد لجهاز NVR
            nvr_ip: عنوان IP
            port: منفذ الاتصال
            username: اسم المستخدم
            password: كلمة المرور
        """
        try:
            connector = NVRConnector(nvr_ip, port, username, password)
            if connector.is_connected:
                self.nvr_connectors[nvr_id] = connector
                self.logger.info(f"تم إضافة NVR: {nvr_id} ({nvr_ip})")
                return True
            else:
                self.logger.error(f"فشل في الاتصال مع NVR: {nvr_id} ({nvr_ip})")
                return False
        except Exception as e:
            self.logger.error(f"خطأ في إضافة NVR {nvr_id}: {str(e)}")
            return False
    
    def start_monitoring(self):
        """بدء مراقبة الكاميرات"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("تم بدء مراقبة الكاميرات")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الكاميرات"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("تم إيقاف مراقبة الكاميرات")
    
    def _monitor_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active:
            try:
                self._check_all_cameras()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                time.sleep(5)
    
    def _check_all_cameras(self):
        """فحص جميع الكاميرات"""
        for nvr_id, connector in self.nvr_connectors.items():
            if not connector.is_connected:
                # محاولة إعادة الاتصال
                connector.connect()
                continue
            
            cameras = connector.get_cameras()
            for camera_id, camera_info in cameras.items():
                self._check_single_camera(nvr_id, camera_id, connector)
    
    def _check_single_camera(self, nvr_id: str, camera_id: str, connector: NVRConnector):
        """فحص كاميرا واحدة"""
        try:
            # فحص حالة الكاميرا
            status = connector.check_camera_status(camera_id)
            
            # إرسال إشارة تحديث الحالة
            self.camera_status_changed.emit(f"{nvr_id}_{camera_id}", status)
            
            # فحص المشاكل
            self._detect_issues(nvr_id, camera_id, status)
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص الكاميرا {camera_id}: {str(e)}")
    
    def _detect_issues(self, nvr_id: str, camera_id: str, status: Dict):
        """كشف المشاكل في الكاميرا"""
        camera_full_id = f"{nvr_id}_{camera_id}"
        
        # فحص انقطاع الاتصال
        if status['status'] == 'offline':
            self.alert_triggered.emit(
                "connection_lost", 
                camera_full_id, 
                f"انقطع الاتصال مع الكاميرا {status.get('name', camera_id)}"
            )
        
        # فحص جودة الصورة
        elif status['status'] == 'online':
            quality = status.get('quality', 0)
            if quality < self.quality_threshold:
                self.alert_triggered.emit(
                    "poor_quality", 
                    camera_full_id, 
                    f"جودة الصورة منخفضة في الكاميرا {status.get('name', camera_id)} ({quality}%)"
                )
            
            # فحص معدل الإطارات
            fps = status.get('fps', 0)
            if fps < 10:  # أقل من 10 إطار في الثانية
                self.alert_triggered.emit(
                    "low_fps", 
                    camera_full_id, 
                    f"معدل الإطارات منخفض في الكاميرا {status.get('name', camera_id)} ({fps} FPS)"
                )
    
    def get_all_cameras(self) -> Dict:
        """الحصول على جميع الكاميرات من جميع أجهزة NVR"""
        all_cameras = {}
        for nvr_id, connector in self.nvr_connectors.items():
            cameras = connector.get_cameras()
            for camera_id, camera_info in cameras.items():
                full_id = f"{nvr_id}_{camera_id}"
                camera_info['nvr_id'] = nvr_id
                all_cameras[full_id] = camera_info
        return all_cameras
    
    def get_camera_stream_url(self, camera_full_id: str) -> str:
        """الحصول على رابط البث المباشر للكاميرا"""
        try:
            nvr_id, camera_id = camera_full_id.split('_', 1)
            if nvr_id in self.nvr_connectors:
                return self.nvr_connectors[nvr_id].get_camera_stream(camera_id)
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على رابط البث: {str(e)}")
        return None
