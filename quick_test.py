#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام مراقبة الكاميرات
Quick Test for Camera Monitoring System
"""

import sys
import random
import time
from datetime import datetime

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                 QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                                 QTextEdit, QGroupBox, QFrame, QMessageBox)
    from PyQt5.QtCore import Qt, QTimer
    from PyQt5.QtGui import QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("تحذير: PyQt5 غير مثبت. سيتم تشغيل النسخة النصية.")

class SimpleCameraMonitor:
    """مراقب كاميرات مبسط للاختبار"""
    
    def __init__(self):
        self.cameras = {}
        self.monitoring = False
        self._create_demo_cameras()
    
    def _create_demo_cameras(self):
        """إنشاء كاميرات تجريبية"""
        camera_names = [
            "كاميرا المدخل الرئيسي",
            "كاميرا الاستقبال", 
            "كاميرا الممر الأول",
            "كاميرا المكتب",
            "كاميرا الخروج الطارئ",
            "كاميرا موقف السيارات"
        ]
        
        for i, name in enumerate(camera_names, 1):
            self.cameras[f"CAM_{i:02d}"] = {
                'name': name,
                'status': 'online',
                'quality': random.randint(70, 100),
                'fps': random.randint(20, 30),
                'last_check': datetime.now()
            }
    
    def check_cameras(self):
        """فحص حالة الكاميرات"""
        alerts = []
        
        for cam_id, camera in self.cameras.items():
            # محاكاة مشاكل عشوائية
            problem_chance = random.randint(1, 100)
            
            if problem_chance <= 15:  # 15% احتمال مشكلة
                if problem_chance <= 5:  # 5% انقطاع
                    camera['status'] = 'offline'
                    camera['quality'] = 0
                    camera['fps'] = 0
                    alerts.append(f"⚠️ انقطع الاتصال مع {camera['name']}")
                else:  # 10% جودة منخفضة
                    camera['status'] = 'online'
                    camera['quality'] = random.randint(20, 50)
                    camera['fps'] = random.randint(5, 15)
                    alerts.append(f"⚠️ جودة منخفضة في {camera['name']} ({camera['quality']}%)")
            else:  # 85% عمل طبيعي
                camera['status'] = 'online'
                camera['quality'] = random.randint(70, 100)
                camera['fps'] = random.randint(20, 30)
            
            camera['last_check'] = datetime.now()
        
        return alerts
    
    def get_status_summary(self):
        """ملخص حالة النظام"""
        total = len(self.cameras)
        online = sum(1 for c in self.cameras.values() if c['status'] == 'online')
        offline = total - online
        
        return {
            'total': total,
            'online': online,
            'offline': offline
        }

if PYQT_AVAILABLE:
    class CameraWidget(QFrame):
        """ويدجت عرض الكاميرا"""
        
        def __init__(self, camera_id, camera_info):
            super().__init__()
            self.camera_id = camera_id
            self.setFrameStyle(QFrame.Box)
            self.setFixedSize(180, 120)
            
            layout = QVBoxLayout(self)
            
            # اسم الكاميرا
            self.name_label = QLabel(camera_info['name'])
            self.name_label.setAlignment(Qt.AlignCenter)
            self.name_label.setFont(QFont("Arial", 9, QFont.Bold))
            layout.addWidget(self.name_label)
            
            # حالة الكاميرا
            self.status_label = QLabel()
            self.status_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(self.status_label)
            
            # معلومات إضافية
            self.info_label = QLabel()
            self.info_label.setFont(QFont("Arial", 8))
            self.info_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(self.info_label)
            
            self.update_status(camera_info)
        
        def update_status(self, camera_info):
            """تحديث حالة الكاميرا"""
            status = camera_info['status']
            
            if status == 'online':
                self.setStyleSheet("QFrame { background-color: #90EE90; border: 2px solid #006400; }")
                status_text = "🟢 متصلة"
            else:
                self.setStyleSheet("QFrame { background-color: #FFB6C1; border: 2px solid #DC143C; }")
                status_text = "🔴 منقطعة"
            
            self.status_label.setText(status_text)
            
            quality = camera_info['quality']
            fps = camera_info['fps']
            self.info_label.setText(f"الجودة: {quality}%\nFPS: {fps}")

    class MainWindow(QMainWindow):
        """النافذة الرئيسية"""
        
        def __init__(self):
            super().__init__()
            self.monitor = SimpleCameraMonitor()
            self.camera_widgets = {}
            self.init_ui()
            self.setup_timer()
        
        def init_ui(self):
            """إعداد الواجهة"""
            self.setWindowTitle("نظام مراقبة الكاميرات - اختبار سريع")
            self.setGeometry(100, 100, 1000, 700)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            main_layout = QHBoxLayout(central_widget)
            
            # منطقة الكاميرات
            cameras_group = QGroupBox("الكاميرات")
            cameras_layout = QVBoxLayout(cameras_group)
            
            self.cameras_grid = QGridLayout()
            cameras_layout.addLayout(self.cameras_grid)
            
            main_layout.addWidget(cameras_group, 3)
            
            # لوحة التحكم
            control_group = QGroupBox("لوحة التحكم")
            control_layout = QVBoxLayout(control_group)
            
            # أزرار التحكم
            self.start_btn = QPushButton("🚀 بدء المراقبة")
            self.start_btn.clicked.connect(self.start_monitoring)
            control_layout.addWidget(self.start_btn)
            
            self.stop_btn = QPushButton("⏹️ إيقاف المراقبة")
            self.stop_btn.clicked.connect(self.stop_monitoring)
            self.stop_btn.setEnabled(False)
            control_layout.addWidget(self.stop_btn)
            
            # معلومات النظام
            info_group = QGroupBox("معلومات النظام")
            info_layout = QVBoxLayout(info_group)
            
            self.info_label = QLabel()
            info_layout.addWidget(self.info_label)
            
            control_layout.addWidget(info_group)
            
            # منطقة التنبيهات
            alerts_group = QGroupBox("التنبيهات")
            alerts_layout = QVBoxLayout(alerts_group)
            
            self.alerts_text = QTextEdit()
            self.alerts_text.setMaximumHeight(200)
            self.alerts_text.setReadOnly(True)
            alerts_layout.addWidget(self.alerts_text)
            
            control_layout.addWidget(alerts_group)
            control_layout.addStretch()
            
            main_layout.addWidget(control_group, 1)
            
            # إنشاء ويدجت الكاميرات
            self.create_camera_widgets()
            self.update_info()
        
        def create_camera_widgets(self):
            """إنشاء ويدجت الكاميرات"""
            row, col = 0, 0
            for cam_id, camera_info in self.monitor.cameras.items():
                widget = CameraWidget(cam_id, camera_info)
                self.camera_widgets[cam_id] = widget
                self.cameras_grid.addWidget(widget, row, col)
                
                col += 1
                if col >= 3:  # 3 كاميرات في كل صف
                    col = 0
                    row += 1
        
        def setup_timer(self):
            """إعداد مؤقت التحديث"""
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_cameras)
        
        def start_monitoring(self):
            """بدء المراقبة"""
            self.monitor.monitoring = True
            self.timer.start(3000)  # تحديث كل 3 ثوان
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.add_alert("✅ تم بدء المراقبة")
        
        def stop_monitoring(self):
            """إيقاف المراقبة"""
            self.monitor.monitoring = False
            self.timer.stop()
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.add_alert("⏹️ تم إيقاف المراقبة")
        
        def update_cameras(self):
            """تحديث حالة الكاميرات"""
            if self.monitor.monitoring:
                alerts = self.monitor.check_cameras()
                
                # تحديث ويدجت الكاميرات
                for cam_id, camera_info in self.monitor.cameras.items():
                    if cam_id in self.camera_widgets:
                        self.camera_widgets[cam_id].update_status(camera_info)
                
                # إضافة التنبيهات
                for alert in alerts:
                    self.add_alert(alert)
                
                self.update_info()
        
        def add_alert(self, message):
            """إضافة تنبيه"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.alerts_text.append(f"[{timestamp}] {message}")
            
            # تمرير لأسفل
            scrollbar = self.alerts_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        
        def update_info(self):
            """تحديث معلومات النظام"""
            status = self.monitor.get_status_summary()
            self.info_label.setText(
                f"📊 إجمالي الكاميرات: {status['total']}\n"
                f"🟢 متصلة: {status['online']}\n"
                f"🔴 منقطعة: {status['offline']}"
            )

def run_gui_test():
    """تشغيل الاختبار مع واجهة رسومية"""
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    # رسالة ترحيب
    QMessageBox.information(
        window,
        "مرحباً بك!",
        "🎉 مرحباً بك في نظام مراقبة الكاميرات!\n\n"
        "📋 تم إنشاء 6 كاميرات تجريبية\n"
        "🚀 اضغط 'بدء المراقبة' لبدء الاختبار\n"
        "⚠️ ستظهر مشاكل عشوائية للاختبار\n\n"
        "💡 هذا عرض توضيحي مع بيانات محاكاة"
    )
    
    sys.exit(app.exec_())

def run_console_test():
    """تشغيل الاختبار في وضع النص"""
    print("=" * 50)
    print("🎥 نظام مراقبة الكاميرات - اختبار نصي")
    print("=" * 50)
    
    monitor = SimpleCameraMonitor()
    
    print(f"\n📊 تم إنشاء {len(monitor.cameras)} كاميرات تجريبية:")
    for cam_id, camera in monitor.cameras.items():
        print(f"  • {cam_id}: {camera['name']}")
    
    print("\n🚀 بدء المراقبة... (اضغط Ctrl+C للإيقاف)")
    
    try:
        cycle = 0
        while True:
            cycle += 1
            print(f"\n--- دورة فحص #{cycle} ---")
            
            alerts = monitor.check_cameras()
            status = monitor.get_status_summary()
            
            print(f"📊 الحالة: {status['online']} متصلة، {status['offline']} منقطعة")
            
            if alerts:
                print("⚠️ التنبيهات:")
                for alert in alerts:
                    print(f"  {alert}")
            else:
                print("✅ لا توجد مشاكل")
            
            print("\n📋 تفاصيل الكاميرات:")
            for cam_id, camera in monitor.cameras.items():
                status_icon = "🟢" if camera['status'] == 'online' else "🔴"
                print(f"  {status_icon} {camera['name']}: {camera['quality']}% جودة، {camera['fps']} FPS")
            
            time.sleep(5)  # انتظار 5 ثوان
            
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف المراقبة")
        print("شكراً لاستخدام نظام مراقبة الكاميرات!")

def main():
    """الدالة الرئيسية"""
    print("🎥 نظام مراقبة الكاميرات - اختبار سريع")
    print("=" * 50)
    
    if PYQT_AVAILABLE:
        print("✅ PyQt5 متوفر - سيتم تشغيل الواجهة الرسومية")
        run_gui_test()
    else:
        print("ℹ️ PyQt5 غير متوفر - سيتم تشغيل النسخة النصية")
        run_console_test()

if __name__ == "__main__":
    main()
