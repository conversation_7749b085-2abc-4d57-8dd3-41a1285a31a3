#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تجريبي لنظام مراقبة الكاميرات
Demo file for Camera Monitoring System

هذا الملف يحتوي على محاكي NVR للاختبار
"""

import sys
import os
import time
import random
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from src.gui.main_window import MainWindow
from src.core.camera_monitor import CameraMonitor
from src.utils.logger import setup_logger

class MockNVRConnector:
    """محاكي NVR للاختبار"""
    
    def __init__(self, nvr_ip, port=8000, username="admin", password=""):
        self.nvr_ip = nvr_ip
        self.port = port
        self.username = username
        self.password = password
        self.is_connected = True
        self.cameras = {}
        
        # إنشاء كاميرات تجريبية
        self._create_demo_cameras()
    
    def _create_demo_cameras(self):
        """إنشاء كاميرات تجريبية"""
        camera_names = [
            "كاميرا المدخل الرئيسي",
            "كاميرا الاستقبال", 
            "كاميرا الممر الأول",
            "كاميرا المكتب",
            "كاميرا الخروج الطارئ",
            "كاميرا موقف السيارات"
        ]
        
        for i, name in enumerate(camera_names, 1):
            self.cameras[str(i)] = {
                'id': str(i),
                'name': name,
                'ip': f"192.168.1.{100 + i}",
                'port': 554,
                'rtsp_url': f"rtsp://192.168.1.{100 + i}:554/stream",
                'status': 'online',
                'last_check': datetime.now(),
                'quality': random.randint(60, 100),
                'fps': random.randint(15, 30)
            }
    
    def connect(self):
        """محاكاة الاتصال"""
        # محاكاة تأخير الاتصال
        time.sleep(0.5)
        self.is_connected = random.choice([True, True, True, False])  # 75% نجاح
        return self.is_connected
    
    def get_cameras(self):
        """الحصول على قائمة الكاميرات"""
        return self.cameras
    
    def check_camera_status(self, camera_id):
        """فحص حالة الكاميرا مع محاكاة مشاكل عشوائية"""
        if camera_id not in self.cameras:
            return {'status': 'not_found', 'message': 'الكاميرا غير موجودة'}
        
        camera = self.cameras[camera_id]
        
        # محاكاة مشاكل عشوائية
        problem_chance = random.randint(1, 100)
        
        if problem_chance <= 10:  # 10% احتمال انقطاع
            camera['status'] = 'offline'
            camera['quality'] = 0
            camera['fps'] = 0
        elif problem_chance <= 25:  # 15% احتمال جودة منخفضة
            camera['status'] = 'online'
            camera['quality'] = random.randint(20, 50)
            camera['fps'] = random.randint(5, 15)
        else:  # 75% عمل طبيعي
            camera['status'] = 'online'
            camera['quality'] = random.randint(70, 100)
            camera['fps'] = random.randint(20, 30)
        
        camera['last_check'] = datetime.now()
        return camera
    
    def get_camera_stream(self, camera_id):
        """الحصول على رابط البث"""
        if camera_id in self.cameras:
            return self.cameras[camera_id]['rtsp_url']
        return None
    
    def disconnect(self):
        """قطع الاتصال"""
        self.is_connected = False

# استبدال الكلاس الأصلي بالمحاكي للاختبار
import src.core.nvr_connector
src.core.nvr_connector.NVRConnector = MockNVRConnector

def run_demo():
    """تشغيل العرض التوضيحي"""
    
    # إعداد نظام السجلات
    logger = setup_logger()
    logger.info("بدء تشغيل العرض التوضيحي")
    
    try:
        # إنشاء تطبيق PyQt5
        app = QApplication(sys.argv)
        app.setApplicationName("نظام مراقبة الكاميرات - عرض توضيحي")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # إنشاء مراقب الكاميرات
        camera_monitor = CameraMonitor()
        
        # ربط مراقب الكاميرات بالواجهة
        main_window.set_camera_monitor(camera_monitor)
        
        # إضافة أجهزة NVR تجريبية
        nvr_configs = [
            {"id": "NVR_01", "ip": "*************", "name": "NVR المبنى الأول"},
            {"id": "NVR_02", "ip": "*************", "name": "NVR المبنى الثاني"},
        ]
        
        for config in nvr_configs:
            success = camera_monitor.add_nvr(
                config["id"], 
                config["ip"], 
                8000, 
                "admin", 
                "123456"
            )
            if success:
                logger.info(f"تم إضافة {config['name']} بنجاح")
            else:
                logger.warning(f"فشل في إضافة {config['name']}")
        
        # عرض النافذة الرئيسية
        main_window.show()
        
        # رسالة ترحيب
        QMessageBox.information(
            main_window, 
            "مرحباً", 
            "مرحباً بك في نظام مراقبة الكاميرات!\n\n"
            "تم إضافة أجهزة NVR تجريبية.\n"
            "اضغط على 'بدء المراقبة' لبدء مراقبة الكاميرات.\n\n"
            "ملاحظة: هذا عرض توضيحي مع بيانات محاكاة."
        )
        
        logger.info("تم تشغيل العرض التوضيحي بنجاح")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل العرض التوضيحي: {str(e)}")
        print(f"خطأ في تشغيل العرض التوضيحي: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_demo()
