#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات لبرنامج فيكتور
Camera Monitoring System for Vector Program

المطور: نظام فيكتور
التاريخ: 2025
الوصف: نظام شامل لمراقبة الكاميرات المتصلة بأجهزة NVR وكشف الأعطال والمخالفات
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import logging

# إضافة مسار المشروع للـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.main_window import MainWindow
from src.core.camera_monitor import CameraMonitor
from src.utils.logger import setup_logger

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    
    # إعداد نظام السجلات
    logger = setup_logger()
    logger.info("بدء تشغيل نظام مراقبة الكاميرات")
    
    try:
        # إنشاء تطبيق PyQt5
        app = QApplication(sys.argv)
        app.setApplicationName("نظام مراقبة الكاميرات - فيكتور")
        app.setApplicationVersion("1.0")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # إنشاء مراقب الكاميرات
        camera_monitor = CameraMonitor()
        
        # ربط مراقب الكاميرات بالواجهة
        main_window.set_camera_monitor(camera_monitor)
        
        # عرض النافذة الرئيسية
        main_window.show()
        
        logger.info("تم تشغيل النظام بنجاح")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {str(e)}")
        print(f"خطأ في تشغيل النظام: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
